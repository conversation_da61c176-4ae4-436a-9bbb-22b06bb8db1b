import { NextRequest, NextResponse } from "next/server"

async function getGeoData() {
  try {
    // Try multiple IP APIs as fallback
    const apis = [
      {
        url: "https://ipapi.co/json/",
        parser: (data: any) => ({
          country: data.country_code || "US",
          city: data.city || "Unknown",
          region: data.region || "Unknown",
          timezone: data.timezone || "UTC",
          ip: data.ip || "Unknown",
        }),
      },
      {
        url: "https://ipinfo.io/json",
        parser: (data: any) => ({
          country: data.country || "US",
          city: data.city || "Unknown",
          region: data.region || "Unknown",
          timezone: data.timezone || "UTC",
          ip: data.ip || "Unknown",
        }),
      },
      {
        url: "https://ip-api.com/json/",
        parser: (data: any) => ({
          country: data.countryCode || "US",
          city: data.city || "Unknown",
          region: data.regionName || "Unknown",
          timezone: data.timezone || "UTC",
          ip: data.query || "Unknown",
        }),
      },
    ]

    for (const api of apis) {
      try {
        const response = await fetch(api.url, {
          headers: { "User-Agent": "Mozilla/5.0" },
          signal: AbortSignal.timeout(5000), // 5 second timeout
        })

        if (response.ok) {
          const data = await response.json()
          const result = api.parser(data)
          console.log(`Geo data from ${api.url}:`, result)
          return result
        }
      } catch (apiError) {
        console.warn(`Failed to fetch from ${api.url}:`, apiError)
        continue
      }
    }

    // All APIs failed
    return {
      country: "US",
      city: "Unknown",
      region: "Unknown",
      timezone: "UTC",
      ip: "Unknown",
    }
  } catch (error) {
    console.error("All geo APIs failed:", error)
    return {
      country: "US",
      city: "Unknown",
      region: "Unknown",
      timezone: "UTC",
      ip: "Unknown",
    }
  }
}

function getLanguageFromCountry(countryCode: string): string {
  const countryToLanguage: Record<string, string> = {
    VN: "vi",
    US: "en",
    GB: "en",
    AU: "en",
    CA: "en",
    JP: "ja",
    KR: "ko",
    TH: "th",
    CN: "zh",
    TW: "zh",
    HK: "zh",
    FR: "fr",
    DE: "de",
    ES: "es",
    IT: "it",
    RU: "ru",
    BR: "pt",
    PT: "pt",
    IN: "hi",
    ID: "id",
    MY: "ms",
    SG: "en",
    PH: "en",
  }

  return countryToLanguage[countryCode] || "en"
}

export async function GET(request: NextRequest) {
  const hostname = request.headers.get("host") || ""
  const isLocalhost = hostname.includes("localhost")

  let geoData
  let detectionMethod = ""

  if (isLocalhost) {
    // Localhost: call real IP API
    geoData = await getGeoData()
    detectionMethod = "ip-api.com"
  } else {
    // Production: use Vercel headers
    const country =
      request.geo?.country || request.headers.get("x-vercel-ip-country") || "US"
    const city =
      request.geo?.city || request.headers.get("x-vercel-ip-city") || "Unknown"
    const region =
      request.geo?.region ||
      request.headers.get("x-vercel-ip-country-region") ||
      "Unknown"
    const timezone = request.headers.get("x-vercel-ip-timezone") || "UTC"

    geoData = {
      country,
      city,
      region,
      timezone,
      ip: request.ip || "Unknown",
    }
    detectionMethod = "vercel-headers"
  }

  const detectedLanguage = getLanguageFromCountry(geoData.country)

  return NextResponse.json({
    ...geoData,
    detectedLanguage,
    detectionMethod,
    isLocalhost,
    hostname,
    userAgent: request.headers.get("user-agent"),
    timestamp: new Date().toISOString(),
  })
}
